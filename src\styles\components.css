h1 { font-size: 26px; margin: 8px 0 18px; }
h2 { font-size: 20px; margin: 6px 0 14px; color: var(--text-dim); }

.form { display: grid; gap: 10px; }
.input, .select, .textarea {
  width: 100%; padding: 10px 12px; border-radius: 10px;
  border: 1px solid #273244; background: #0b1324; color: var(--text);
  outline: none;
}
.input:focus, .textarea:focus { border-color: #334155; }
.btn {
  padding: 10px 14px; border: 1px solid #303a4d; border-radius: 10px;
  background: linear-gradient(180deg, #232a3f, #1a2135);
  color: var(--text); transition: transform .08s ease, box-shadow .15s ease;
}
.btn:hover { transform: translateY(-1px); box-shadow: 0 8px 20px rgba(0,0,0,0.25); }
.btn.primary { background: linear-gradient(180deg, #4f46e5, #4338ca); border-color: transparent; }
.btn.danger { background: linear-gradient(180deg, #ef4444, #dc2626); border-color: transparent; }

.table { width: 100%; border-collapse: collapse; }
.table th, .table td {
  text-align: left; padding: 10px 12px; border-bottom: 1px solid #1f2937;
}
.table th { color: var(--text-dim); font-weight: 600; }
.tag { padding: 3px 8px; border-radius: 8px; background: #0f1a34; border: 1px solid #233155; color: var(--text-dim); }
.row { display: flex; align-items: center; gap: 8px; }
