import { useState } from 'react';
import PostForm from '../components/PostForm';
import PostsTable from '../components/PostsTable';

export default function PostsPage() {
  const [refresh, setRefresh] = useState(0);
  return (
    <div className="container">
      <h1>Posts</h1>
      <div className="grid">
        <PostForm onCreated={() => setRefresh(r => r + 1)} />
        <PostsTable refreshSignal={refresh} />
      </div>
    </div>
  );
}
