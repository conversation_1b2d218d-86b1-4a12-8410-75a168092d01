import { useState } from 'react';
import api from '../api/axiosInstance';

export default function PostForm({ onCreated }) {
  const [form, setForm] = useState({ title: '', content: '', userId: '' });
  const [loading, setLoading] = useState(false);

  const change = e => setForm({ ...form, [e.target.name]: e.target.value });

  const submit = async e => {
    e.preventDefault();
    setLoading(true);
    try {
      const { data } = await api.post('/posts', {
        title: form.title,
        content: form.content,
        userId: Number(form.userId)
      });
      onCreated?.(data);
      setForm({ title: '', content: '', userId: '' });
    } catch (err) {
      alert(err.response?.data?.error || 'Create post failed');
    } finally { setLoading(false); }
  };

  return (
    <div className="panel">
      <h2>Create Post</h2>
      <form className="form" onSubmit={submit}>
        <input className="input" name="title" placeholder="Title" value={form.title} onChange={change} />
        <textarea className="textarea" name="content" placeholder="Content…" rows="4" value={form.content} onChange={change} />
        <input className="input" name="userId" placeholder="Owner User ID" value={form.userId} onChange={change} />
        <button className="btn primary" disabled={loading}>{loading ? 'Saving...' : 'Create Post'}</button>
      </form>
    </div>
  );
}
