import { useEffect, useState } from 'react';
import api from '../api/axiosInstance';

export default function UsersTable({ refreshSignal }) {
  const [user, setUser] = useState(null);
  const [emailQuery, setEmailQuery] = useState('');

  const find = async () => {
    if (!emailQuery.trim()) return;
    try {
      const { data } = await api.get(`/users/by-email?email=${encodeURIComponent(emailQuery)}`);
      setUser(data);
    // eslint-disable-next-line no-unused-vars
    } catch (err) {
      alert('Not found or error');
    }
  };

  useEffect(() => { /* placeholder for future list fetch */ }, [refreshSignal]);

  return (
    <div className="panel">
      <h2>Find User by Email</h2>
      <div className="row" style={{ gap: 12 }}>
        <input className="input" placeholder="<EMAIL>" value={emailQuery} onChange={e => setEmailQuery(e.target.value)} />
        <button className="btn" onClick={find}>Search</button>
      </div>

      {user && (
        <div style={{ marginTop: 12 }}>
          <table className="table">
            <thead>
              <tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th></tr>
            </thead>
            <tbody>
              <tr>
                <td>{user?.id}</td>
                <td>{user?.name}</td>
                <td>{user?.email}</td>
                <td><span className="tag">{user?.role}</span></td>
              </tr>
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
