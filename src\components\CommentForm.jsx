import { useState } from 'react';
import api from '../api/axiosInstance';

export default function CommentForm({ onBulkCreated }) {
  const [rows, setRows] = useState([
    { content: '', postId: '', userId: '' }
  ]);

  const addRow = () => setRows(r => [...r, { content: '', postId: '', userId: '' }]);
  const change = (i, k, v) => setRows(r => r.map((row, idx) => idx === i ? { ...row, [k]: v } : row));

  const submit = async e => {
    e.preventDefault();
    try {
      const payload = rows.map(r => ({ content: r.content, postId: Number(r.postId), userId: Number(r.userId) }));
      const { data } = await api.post('/comments', payload);
      onBulkCreated?.(data);
      setRows([{ content: '', postId: '', userId: '' }]);
    } catch (err) {
      alert(err.response?.data?.error || 'Bulk create failed');
    }
  };

  return (
    <div className="panel">
      <h2>Bulk Create Comments</h2>
      <form className="form" onSubmit={submit}>
        {rows.map((r, i) => (
          <div className="grid" key={i}>
            <input className="input" placeholder="Content" value={r.content} onChange={e => change(i, 'content', e.target.value)} />
            <input className="input" placeholder="Post ID" value={r.postId} onChange={e => change(i, 'postId', e.target.value)} />
            <input className="input" placeholder="User ID" value={r.userId} onChange={e => change(i, 'userId', e.target.value)} />
          </div>
        ))}
        <div className="row" style={{ gap: 8 }}>
          <button type="button" className="btn" onClick={addRow}>+ Add Row</button>
          <button className="btn primary" type="submit">Create Comments</button>
        </div>
      </form>
    </div>
  );
}
