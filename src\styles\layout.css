.container { max-width: 1100px; margin: 0 auto; padding: 24px; }
.panel {
  background: linear-gradient(180deg, #111827, #0f172a);
  border: 1px solid #1f2937;
  border-radius: 14px;
  padding: 18px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.35), inset 0 1px 0 rgba(255,255,255,0.02);
}

.grid {
  display: grid; gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

header.nav {
  position: sticky; top: 0; z-index: 10;
  backdrop-filter: blur(8px);
  background: rgba(15, 23, 42, 0.6);
  border-bottom: 1px solid #1f2937;
}
.nav-inner {
  display: flex; align-items: center; justify-content: space-between;
  max-width: 1100px; margin: 0 auto; padding: 12px 24px;
}
.nav a { margin-right: 16px; color: var(--text-dim); }
.nav a.active { color: var(--text); font-weight: 600; }
