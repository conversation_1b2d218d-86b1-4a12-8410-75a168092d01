import { useState } from 'react';
import api from '../api/axiosInstance';

export default function UserForm({ onCreated }) {
  const [form, setForm] = useState({ name: '', email: '', password: '' });
  const [loading, setLoading] = useState(false);

  const change = e => setForm({ ...form, [e.target.name]: e.target.value });

  const submit = async e => {
    e.preventDefault();
    setLoading(true);
    try {
      const { data } = await api.post('/users/signup', form);
      onCreated?.(data);
      setForm({ name: '', email: '', password: '' });
    } catch (err) {
        console.log(err);
        
      alert(err.response?.data?.error || 'Create failed');
    } finally { setLoading(false); }
  };

  return (
    <div className="panel">
      <h2>Create User</h2>
      <form className="form" onSubmit={submit}>
        <input className="input" name="name" placeholder="Name" value={form.name} onChange={change} />
        <input className="input" name="email" placeholder="Email" value={form.email} onChange={change} />
        <input className="input" type="password" name="password" placeholder="Password" value={form.password} onChange={change} />
        <button className="btn primary" disabled={loading}>{loading ? 'Saving...' : 'Create'}</button>
      </form>
    </div>
  );
}
