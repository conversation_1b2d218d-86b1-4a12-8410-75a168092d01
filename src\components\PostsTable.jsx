import { useEffect, useState } from 'react';
import api from '../api/axiosInstance';

export default function PostsTable({ refreshSignal }) {
  const [posts, setPosts] = useState([]);
  const [deleteState, setDeleteState] = useState({ postId: '', userId: '' });

  const load = async () => {
    const { data } = await api.get('/posts/details');
    setPosts(data);
  };

  const remove = async () => {
    if (!deleteState.postId || !deleteState.userId) return;
    try {
      await api.delete(`/posts/${deleteState.postId}`, { data: { userId: Number(deleteState.userId) } });
      await load();
      setDeleteState({ postId: '', userId: '' });
    } catch (err) {
      alert(err.response?.data?.error || 'Delete failed');
    }
  };

  useEffect(() => { load(); }, [refreshSignal]);

  return (
    <div className="panel">
      <h2>Posts (with user & comments)</h2>

      <div className="row" style={{ gap: 8, marginBottom: 10 }}>
        <input className="input" placeholder="Post ID" value={deleteState.postId} onChange={e => setDeleteState(s => ({...s, postId: e.target.value}))} />
        <input className="input" placeholder="Owner User ID" value={deleteState.userId} onChange={e => setDeleteState(s => ({...s, userId: e.target.value}))} />
        <button className="btn danger" onClick={remove}>Delete</button>
      </div>

      <table className="table">
        <thead>
          <tr><th>ID</th><th>Title</th><th>User</th><th>Comments</th></tr>
        </thead>
        <tbody>
          {posts.map(p => (
            <tr key={p.id}>
              <td>{p.id}</td>
              <td>{p.title}</td>
              <td>{p.User?.name} <span className="tag">#{p.User?.id}</span></td>
              <td>{p.Comments?.length ?? 0}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
