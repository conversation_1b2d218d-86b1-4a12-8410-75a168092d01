import { useState } from 'react';
import UserForm from '../components/UserForm';
import UsersTable from '../components/UsersTable';

export default function UsersPage() {
  const [refresh, setRefresh] = useState(0);
  return (
    <div className="container  ">
      <h1 className=''>Users</h1>
      <div className="grid">
        <UserForm onCreated={() => setRefresh(r => r + 1)} />
        <UsersTable refreshSignal={refresh} />
      </div>
    </div>
  );
}
