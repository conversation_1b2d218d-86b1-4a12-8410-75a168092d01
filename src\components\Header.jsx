import { NavLink } from 'react-router-dom';

export default function Header() {
  return (
    <header className="nav">
      <div className="nav-inner">
        <div className="row">
          <strong>Sequelize Admin</strong>
        </div>
        <nav className="row">
          <NavLink to="/users" className={({isActive}) => isActive ? 'active' : ''}>Users</NavLink>
          <NavLink to="/posts" className={({isActive}) => isActive ? 'active' : ''}>Posts</NavLink>
          <NavLink to="/comments" className={({isActive}) => isActive ? 'active' : ''}>Comments</NavLink>
        </nav>
      </div>
    </header>
  );
}
