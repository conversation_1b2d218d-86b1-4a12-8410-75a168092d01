import { useState } from 'react';
import CommentForm from '../components/CommentForm';
import CommentsTable from '../components/CommentsTable';

export default function CommentsPage() {
  const [refresh, setRefresh] = useState(0);
  return (
    <div className="container">
      <h1>Comments</h1>
      <div className="grid">
        <CommentForm onBulkCreated={() => setRefresh(r => r + 1)} />
        <CommentsTable refreshSignal={refresh} />
      </div>
    </div>
  );
}
