// src/components/CommentsTable.jsx
import { useEffect, useState } from 'react';
import api from '../api/axiosInstance';

export default function CommentsTable({ refreshSignal }) {
  const [postId, setPostId] = useState('');
  const [newest, setNewest] = useState([]);
  const [searchWord, setSearchWord] = useState('');
  const [searchRes, setSearchRes] = useState(null);

  const loadNewest = async () => {
    if (!postId) return;
    try {
      const { data } = await api.get(`/comments/newest/${postId}`);
      setNewest(data);
    } catch (e) {
      console.error(e);
    }
  };

  const doSearch = async () => {
    if (!searchWord.trim()) return;
    try {
      const { data } = await api.get(`/comments/search?word=${encodeURIComponent(searchWord)}`);
      setSearchRes(data); // { count, rows }
    } catch (e) {
      console.error(e);
      setSearchRes({ count: 0, rows: [] });
    }
  };

  // Enter triggers search
  const onSearchKey = (e) => {
    if (e.key === 'Enter') doSearch();
  };

  useEffect(() => {
    if (postId) loadNewest();
  }, [refreshSignal]); // reload on signal

  return (
    <div className="panel">
      <h2>Comments</h2>

      {/* Newest 3 for a post */}
      <div className="row" style={{ gap: 8, marginBottom: 12 }}>
        <input
          className="input"
          placeholder="Post ID for newest 3"
          value={postId}
          onChange={(e) => setPostId(e.target.value)}
        />
        <button className="btn" onClick={loadNewest}>Load Newest</button>
      </div>

      {newest.length > 0 && (
        <table className="table" style={{ marginBottom: 18 }}>
          <thead>
            <tr><th>ID</th><th>Content</th><th>User</th><th>Created</th></tr>
          </thead>
          <tbody>
            {newest.map(c => (
              <tr key={c.id}>
                <td>{c.id}</td>
                <td>{c.content}</td>
                <td>#{c.userId}</td>
                <td>{new Date(c.createdAt).toLocaleString()}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      {/* Search by word */}
      <div className="row" style={{ gap: 8, marginBottom: 12 }}>
        <input
          className="input"
          placeholder="Search word in content"
          value={searchWord}
          onChange={(e) => setSearchWord(e.target.value)}
          onKeyDown={onSearchKey}
        />
        <button className="btn" onClick={doSearch}>Search</button>
      </div>

      {searchRes && (
        <>
          <div className="tag">Matched count: {searchRes.count}</div>

          {Array.isArray(searchRes.rows) && searchRes.rows.length > 0 && (
            <table className="table" style={{ marginTop: 10 }}>
              <thead>
                <tr><th>ID</th><th>Content</th><th>User</th><th>Created</th></tr>
              </thead>
              <tbody>
                {searchRes.rows.map(c => (
                  <tr key={c.id}>
                    <td>{c.id}</td>
                    <td>{c.content}</td>
                    <td>#{c.userId}</td>
                    <td>{new Date(c.createdAt).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </>
      )}
    </div>
  );
}
